import json
import re
import sys
import os
from datetime import datetime

def parse_chat_file(file_path, output_to_file=False):
    """解析聊天历史文件并整理成可读格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            # 尝试使用不同的编码
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return

    # 准备输出
    if output_to_file:
        base_name = os.path.basename(file_path)
        output_file = f"parsed_{base_name}"
        old_stdout = sys.stdout
        sys.stdout = open(output_file, 'w', encoding='utf-8')
        print(f"解析文件: {file_path}")
        print(f"输出保存到: {output_file}\n")

    # 提取Cursor聊天记录的各个部分
    sections = {}
    
    # 提取Composer数据
    composer_section = re.search(r'=== Composer数据 ===\s*(.*?)(?===)', content, re.DOTALL)
    if composer_section:
        sections['composer'] = composer_section.group(1).strip()
        
    # 提取用户提示历史
    prompts_section = re.search(r'=== 用户提示历史 ===\s*(.*?)(?===|$)', content, re.DOTALL)
    if prompts_section:
        sections['prompts'] = prompts_section.group(1).strip()
    
    # 提取AI回复（可能在不同位置）
    ai_responses_section = re.search(r'=== AI回复 ===\s*(.*?)(?===|$)', content, re.DOTALL)
    if ai_responses_section:
        sections['ai_responses'] = ai_responses_section.group(1).strip()
    
    # 尝试提取聊天记录
    chat_records = []
    
    # 从用户提示历史中提取
    if 'prompts' in sections:
        prompt_blocks = re.findall(r'--- 提示 \d+ ---\s*(.*?)(?=--- 提示 \d+ ---|$)', sections['prompts'], re.DOTALL)
        for block in prompt_blocks:
            # 提取用户信息
            user_match = re.search(r'\[用户\]:\s*(.*?)(?=commandType:|$)', block, re.DOTALL)
            if user_match:
                user_message = user_match.group(1).strip()
                timestamp_match = re.search(r'timestamp:\s*(\d+)', block)
                timestamp = timestamp_match.group(1) if timestamp_match else None
                
                chat_record = {
                    'role': 'user',
                    'content': user_message
                }
                if timestamp:
                    try:
                        time_str = datetime.fromtimestamp(int(timestamp)/1000).strftime('%Y-%m-%d %H:%M:%S')
                        chat_record['timestamp'] = time_str
                    except:
                        chat_record['timestamp'] = timestamp
                
                chat_records.append(chat_record)
    
    # 从AI回复中提取（如果有）
    if 'ai_responses' in sections:
        response_blocks = re.findall(r'--- 回复 \d+ ---\s*(.*?)(?=--- 回复 \d+ ---|$)', sections['ai_responses'], re.DOTALL)
        for i, block in enumerate(response_blocks):
            # 提取AI回复
            ai_match = re.search(r'\[AI\]:\s*(.*?)(?=$)', block, re.DOTALL)
            if ai_match:
                ai_message = ai_match.group(1).strip()
                timestamp_match = re.search(r'timestamp:\s*(\d+)', block)
                timestamp = timestamp_match.group(1) if timestamp_match else None
                
                chat_record = {
                    'role': 'assistant',
                    'content': ai_message
                }
                if timestamp:
                    try:
                        time_str = datetime.fromtimestamp(int(timestamp)/1000).strftime('%Y-%m-%d %H:%M:%S')
                        chat_record['timestamp'] = time_str
                    except:
                        chat_record['timestamp'] = timestamp
                
                # 如果AI回复数量与用户提示数量相同，则插入到对应位置
                if i < len(chat_records):
                    chat_records.insert(2*i+1, chat_record)
                else:
                    chat_records.append(chat_record)
    
    # 尝试从其他格式中提取
    if not chat_records:
        # 尝试解析JSON格式的聊天记录
        try:
            # 检查文件是否为JSON数组
            if content.strip().startswith('[') and content.strip().endswith(']'):
                chats = json.loads(content)
                output_json_chats(chats)
                if output_to_file:
                    sys.stdout.close()
                    sys.stdout = old_stdout
                    print(f"已解析聊天记录并保存到 {output_file}")
                return
        except json.JSONDecodeError:
            pass
            
        # 尝试提取消息模式1: {"role": "user", "content": "..."}
        pattern1 = r'{"role":\s*"(user|assistant|system)",\s*"content":\s*"(.*?)"}(?=,\s*{"role"|])'
        matches1 = re.findall(pattern1, content, re.DOTALL)
        if matches1:
            for role, msg_content in matches1:
                chat_records.append({
                    "role": role,
                    "content": msg_content.replace('\\n', '\n').replace('\\"', '"')
                })
        
        # 尝试提取消息模式2: 其他可能的格式
        if not chat_records:
            # 尝试寻找时间戳和消息内容
            pattern2 = r'"timestamp":\s*(\d+).*?"content":\s*"(.*?)"'
            matches2 = re.findall(pattern2, content, re.DOTALL)
            for timestamp, msg_content in matches2:
                try:
                    time_str = datetime.fromtimestamp(int(timestamp)/1000).strftime('%Y-%m-%d %H:%M:%S')
                    chat_records.append({
                        "timestamp": time_str,
                        "content": msg_content.replace('\\n', '\n').replace('\\"', '"')
                    })
                except:
                    pass

    # 输出解析结果
    if sections:
        print("=== 文件结构分析 ===")
        for section_name, section_content in sections.items():
            print(f"\n--- {section_name} 部分 ---")
            print(f"长度: {len(section_content)} 字符")
    
    # 如果找到聊天记录，输出整理后的结果
    if chat_records:
        print("\n=== 整理后的聊天记录 ===\n")
        for i, record in enumerate(chat_records, 1):
            print(f"--- 消息 {i} ---")
            if 'timestamp' in record:
                print(f"时间: {record['timestamp']}")
            print(f"[{record['role']}]:")
            print(record['content'])
            print()
    else:
        # 如果没有找到结构化的聊天记录，尝试提取所有可能的用户提示和AI回复
        print("\n=== 尝试提取所有用户提示和AI回复 ===\n")
        
        # 提取所有用户提示
        user_prompts = re.findall(r'\[用户\]:\s*(.*?)(?=\[用户\]:|commandType:|$)', content, re.DOTALL)
        if user_prompts:
            print("--- 用户提示 ---")
            for i, prompt in enumerate(user_prompts, 1):
                print(f"{i}. {prompt.strip()}")
            print()
        
        # 提取所有可能的AI回复
        ai_responses = re.findall(r'\[AI\]:\s*(.*?)(?=\[AI\]:|$)', content, re.DOTALL)
        if ai_responses:
            print("--- AI回复 ---")
            for i, response in enumerate(ai_responses, 1):
                print(f"{i}. {response.strip()}")
            print()
            
        if not user_prompts and not ai_responses:
            print("无法识别文件格式。尝试直接输出文件的前1000个字符:")
            print(content[:1000])
            print("\n...\n")
            print("文件末尾的1000个字符:")
            print(content[-1000:])

    if output_to_file:
        sys.stdout.close()
        sys.stdout = old_stdout
        print(f"已解析聊天记录并保存到 {output_file}")

def output_json_chats(chats):
    """输出JSON格式的聊天记录"""
    print(f"找到 {len(chats)} 条聊天记录")
    for i, chat in enumerate(chats, 1):
        print(f"\n--- 聊天 {i} ---")
        
        # 提取时间信息
        timestamp = chat.get('timestamp')
        if timestamp:
            try:
                time_str = datetime.fromtimestamp(int(timestamp)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"时间: {time_str}")
            except:
                print(f"时间戳: {timestamp}")
        
        # 提取消息
        messages = chat.get('messages', [])
        for msg in messages:
            role = msg.get('role', '未知')
            content = msg.get('content', '')
            print(f"\n[{role}]:")
            print(content)

def output_formatted_messages(messages):
    """输出格式化的消息"""
    print(f"找到 {len(messages)} 条消息")
    for i, msg in enumerate(messages, 1):
        print(f"\n--- 消息 {i} ---")
        
        # 输出时间戳(如果有)
        if 'timestamp' in msg:
            print(f"时间: {msg['timestamp']}")
            
        # 输出角色和内容
        if 'role' in msg:
            print(f"[{msg['role']}]:")
        print(msg.get('content', ''))

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        output_to_file = "--save" in sys.argv
        parse_chat_file(file_path, output_to_file)
    else:
        print("用法: python parse_chat_history.py <聊天历史文件路径> [--save]")
        print("例如: python parse_chat_history.py cursor_chats_1560e786fefbcef87d03ec641ba505d0.txt --save")
        print("--save: 将输出保存到文件而不是显示在控制台") 