#!/usr/bin/env python3
import sqlite3
import json
import os
import sys
from pathlib import Path

def list_all_keys(db_path):
    """列出数据库中所有键"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询所有键
        cursor.execute("SELECT [key] FROM ItemTable")
        
        keys = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        # 过滤可能与聊天相关的键
        chat_related_keys = [key for key in keys if any(term in key.lower() for term in 
                                                      ['chat', 'ai', 'message', 'prompt', 'conversation', 'history', 'composer'])]
        
        return chat_related_keys
    
    except sqlite3.Error as e:
        print(f"SQLite错误: {e}")
        return []
    except Exception as e:
        print(f"错误: {e}")
        return []

def extract_chats_from_db(db_path):
    """从数据库中提取聊天记录"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有可能与聊天相关的键
        chat_related_keys = list_all_keys(db_path)
        print(f"找到 {len(chat_related_keys)} 个可能与聊天相关的键:")
        for key in chat_related_keys:
            print(f"  - {key}")
        
        # 扩展查询范围，包括更多可能包含聊天记录的键
        query_keys = [
            'aiService.prompts', 
            'workbench.panel.aichat.view.aichat.chatdata',
            'composer.composerData',
            'workbench.panel.aichat.view.aichat.history',
            'workbench.panel.aichat.view.aichat',
            'aiService.history',
            'aiService.conversations'
        ]
        
        # 添加找到的其他可能相关的键
        for key in chat_related_keys:
            if key not in query_keys:
                query_keys.append(key)
        
        # 构建查询
        placeholders = ', '.join(['?' for _ in query_keys])
        query = f"SELECT [key], value FROM ItemTable WHERE [key] IN ({placeholders})"
        
        # 执行查询
        cursor.execute(query, query_keys)
        
        results = cursor.fetchall()
        conn.close()
        
        if not results:
            print(f"在 {db_path} 中没有找到聊天记录")
            return None
        
        # 处理结果
        chat_data = {}
        for key, value in results:
            try:
                # 解析JSON数据
                parsed_value = json.loads(value)
                chat_data[key] = parsed_value
            except json.JSONDecodeError:
                print(f"无法解析 {key} 的JSON数据")
                chat_data[key] = value
        
        return chat_data
    
    except sqlite3.Error as e:
        print(f"SQLite错误: {e}")
        return None
    except Exception as e:
        print(f"错误: {e}")
        return None

def format_chat_data(chat_data):
    """格式化聊天数据以便于阅读"""
    if not chat_data:
        return "没有找到聊天记录"
    
    formatted_output = []
    
    # 处理聊天数据 - workbench.panel.aichat.view.aichat.chatdata
    if 'workbench.panel.aichat.view.aichat.chatdata' in chat_data:
        chat = chat_data['workbench.panel.aichat.view.aichat.chatdata']
        formatted_output.append("\n=== 聊天记录 (chatdata) ===")
        
        if isinstance(chat, dict) and 'chatTabs' in chat:
            for tab_idx, tab in enumerate(chat['chatTabs']):
                tab_title = tab.get('title', f'未命名对话 {tab_idx+1}')
                formatted_output.append(f"\n--- 聊天标签: {tab_title} ---")
                
                if 'messages' in tab:
                    for msg_idx, msg in enumerate(tab['messages']):
                        role = msg.get('role', '未知')
                        content = msg.get('content', '')
                        
                        # 格式化角色标签
                        if role == 'user':
                            role_label = '用户'
                        elif role == 'assistant':
                            role_label = 'AI助手'
                        else:
                            role_label = role
                            
                        formatted_output.append(f"\n[{role_label}]:")
                        formatted_output.append(content)
                        
                        # 添加分隔线，使对话更易读
                        if msg_idx < len(tab['messages']) - 1:
                            formatted_output.append("\n" + "-" * 40)
        else:
            formatted_output.append(str(chat))
    
    # 处理composer数据
    if 'composer.composerData' in chat_data:
        composer_data = chat_data['composer.composerData']
        formatted_output.append("\n=== Composer数据 ===")
        
        if isinstance(composer_data, dict):
            # 尝试提取composer中的聊天记录
            if 'allComposers' in composer_data:
                for comp_idx, composer in enumerate(composer_data['allComposers']):
                    comp_name = composer.get('name', f'未命名Composer {comp_idx+1}')
                    formatted_output.append(f"\n--- Composer: {comp_name} ---")
                    
                    # 提取更多composer详情
                    for key, value in composer.items():
                        if key != 'name':
                            formatted_output.append(f"{key}: {value}")
            
            # 尝试提取选中的聊天ID
            if 'selectedChatId' in composer_data:
                formatted_output.append(f"\n选中的聊天ID: {composer_data['selectedChatId']}")
        else:
            formatted_output.append(str(composer_data))
    
    # 处理其他可能包含聊天记录的键
    for key, value in chat_data.items():
        if key not in ['workbench.panel.aichat.view.aichat.chatdata', 'aiService.prompts', 'composer.composerData']:
            formatted_output.append(f"\n=== {key} ===")
            
            # 尝试格式化JSON数据
            if isinstance(value, dict) or isinstance(value, list):
                formatted_output.append(json.dumps(value, indent=2, ensure_ascii=False))
            else:
                formatted_output.append(str(value))
    
    # 处理aiService.prompts (仅提取用户提示)
    if 'aiService.prompts' in chat_data:
        prompts = chat_data['aiService.prompts']
        formatted_output.append("\n=== 用户提示历史 ===")
        
        if isinstance(prompts, list):
            for i, prompt in enumerate(prompts):
                if isinstance(prompt, dict) and 'text' in prompt:
                    formatted_output.append(f"\n--- 提示 {i+1} ---")
                    formatted_output.append(f"[用户]: {prompt['text']}")
                    # 添加更多提示详情
                    for key, value in prompt.items():
                        if key != 'text':
                            formatted_output.append(f"{key}: {value}")
        else:
            formatted_output.append("无法解析提示历史")
    
    return "\n".join(formatted_output)

def main():
    # 获取Cursor工作区存储路径
    workspace_storage = Path(os.environ['APPDATA']) / 'Cursor' / 'User' / 'workspaceStorage'
    
    if not workspace_storage.exists():
        print(f"找不到Cursor工作区存储路径: {workspace_storage}")
        return
    
    # 尝试查找其他可能的存储位置
    other_possible_paths = [
        Path(os.environ['LOCALAPPDATA']) / 'Cursor' / 'User' / 'workspaceStorage',
        Path(os.environ['APPDATA']) / 'Cursor' / 'User' / 'History',
        Path(os.environ['LOCALAPPDATA']) / 'Cursor' / 'User' / 'History',
        Path(os.environ['APPDATA']) / 'Cursor' / 'Backups',
        Path(os.environ['LOCALAPPDATA']) / 'Cursor' / 'Backups'
    ]
    
    # 检查其他可能的路径
    for path in other_possible_paths:
        if path.exists():
            print(f"找到其他可能的存储位置: {path}")
            # 如果是目录，列出其内容
            if path.is_dir():
                print("该目录包含以下内容:")
                for item in path.iterdir():
                    print(f"  - {item.name}")
    
    # 遍历所有工作区文件夹
    workspace_folders = [f for f in workspace_storage.iterdir() if f.is_dir()]
    
    if not workspace_folders:
        print("没有找到工作区文件夹")
        return
    
    print(f"找到 {len(workspace_folders)} 个工作区文件夹")
    print("\n注意: 如果看到乱码，请确保终端支持UTF-8编码")
    print("=" * 60)
    
    for i, folder in enumerate(workspace_folders):
        db_path = folder / 'state.vscdb'
        
        if not db_path.exists():
            print(f"工作区 {i+1}: {folder.name} - 没有找到state.vscdb文件")
            continue
        
        print(f"\n处理工作区 {i+1}: {folder.name}")
        
        # 检查文件大小
        file_size_mb = db_path.stat().st_size / (1024 * 1024)
        print(f"数据库文件大小: {file_size_mb:.2f} MB")
        
        # 检查是否有备份文件
        backup_path = folder / 'state.vscdb.backup'
        if backup_path.exists():
            backup_size_mb = backup_path.stat().st_size / (1024 * 1024)
            print(f"发现备份文件: {backup_path.name}, 大小: {backup_size_mb:.2f} MB")
        
        chat_data = extract_chats_from_db(str(db_path))
        
        if chat_data:
            formatted_chat = format_chat_data(chat_data)
            
            # 保存到文件 (使用UTF-8编码)
            output_file = Path(os.environ['USERPROFILE']) / 'Desktop' / f"cursor_chats_{folder.name}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(formatted_chat)
            
            print(f"聊天记录已保存到: {output_file}")
            
            # 直接在控制台显示一部分内容
            print("\n预览 (部分内容):")
            print("=" * 60)
            
            lines = formatted_chat.split('\n')
            # 只显示前15行，避免输出过多
            for line in lines[:15]:
                if len(line) > 100:
                    print(line[:100] + "...")
                else:
                    print(line)
            
            if len(lines) > 15:
                print("\n... (更多内容已保存到桌面文件) ...")
            
            print("=" * 60)
            
            # 如果有备份文件，也尝试提取
            if backup_path.exists():
                print(f"\n尝试从备份文件提取聊天记录: {backup_path.name}")
                backup_chat_data = extract_chats_from_db(str(backup_path))
                
                if backup_chat_data:
                    backup_formatted_chat = format_chat_data(backup_chat_data)
                    
                    # 保存到文件
                    backup_output_file = Path(os.environ['USERPROFILE']) / 'Desktop' / f"cursor_chats_{folder.name}_backup.txt"
                    with open(backup_output_file, 'w', encoding='utf-8') as f:
                        f.write(backup_formatted_chat)
                    
                    print(f"备份聊天记录已保存到: {backup_output_file}")

if __name__ == "__main__":
    main() 
 